# ACL-based OCR Pipeline (om_ocr)

This is an OCR pipeline implementation using the Ascend ACL inference framework, adapted from the OMocr_onnxruntime architecture.

## Features

- **Detection**: Text detection using ACL inference with .om models
- **Recognition**: Text recognition using ACL inference with .om models  
- **No Classification**: Classification module is disabled in this implementation
- **Same Interface**: Maintains the same API as rapidocr_onnxruntime for easy migration

## Architecture

```
om_ocr/
├── __init__.py                 # Package initialization
├── main.py                     # Main OCR pipeline (OMOCR class)
├── config.yaml                 # Configuration file for .om models
├── test_ocr.py                 # Test script
├── utils/                      # Utility modules
│   ├── __init__.py
│   ├── acl_infer_session.py    # ACL inference wrapper
│   ├── load_image.py           # Image loading utilities
│   ├── logger.py               # Logging utilities
│   ├── process_img.py          # Image processing utilities
│   └── vis_res.py              # Visualization utilities
├── ch_ppocr_det/               # Detection module
│   ├── __init__.py
│   ├── text_detect.py          # Text detector using ACL
│   └── utils.py                # Detection preprocessing/postprocessing
├── ch_ppocr_rec/               # Recognition module
│   ├── __init__.py
│   ├── text_recognize.py       # Text recognizer using ACL
│   └── utils.py                # Recognition preprocessing/postprocessing
└── cal_rec_boxes/              # Character box calculation
    ├── __init__.py
    └── main.py                 # Character-level box calculation
```

## Key Differences from rapidocr_onnxruntime

1. **Inference Engine**: Uses `AclInferSession` instead of `OrtInferSession`
2. **Model Format**: Uses `.om` models instead of `.onnx` models
3. **Device Configuration**: Supports ACL device_id configuration
4. **No Classification**: cls module is disabled
5. **ACL Integration**: Follows the ACL_Net usage pattern from inference.py

## Configuration

The `config.yaml` file contains model paths and parameters:

```yaml
Global:
    text_score: 0.5
    use_det: true
    use_cls: false          # Always false for ACL implementation
    use_rec: true
    # ... other global settings

Det:
    device_id: 0            # ACL device ID
    model_path: models/ch_PP-OCRv4_det_infer.om  # .om model path
    # ... detection parameters

Rec:
    device_id: 0            # ACL device ID  
    model_path: models/ch_PP-OCRv4_rec_infer.om  # .om model path
    rec_keys_path: models/ppocr_keys_v1.txt      # Character dictionary
    # ... recognition parameters
```

## Usage

### Basic Usage

```python
from om_ocr import OMOCR

# Initialize OCR engine
ocr_engine = OMOCR(config_path="config.yaml")

# Run OCR on an image
result, elapse_list = ocr_engine("path/to/image.jpg")

# Process results
if result:
    for box, text, score in result:
        print(f"Text: {text}, Score: {score}")
        print(f"Box: {box}")
```

### Command Line Testing

```bash
cd ocr_pipeline/om_ocr
python test_ocr.py --img_path /path/to/image.jpg --config_path config.yaml --print_cost
```

### Advanced Usage

```python
# Disable detection (recognition only)
result, elapse = ocr_engine("image.jpg", use_det=False, use_rec=True)

# Disable recognition (detection only) 
result, elapse = ocr_engine("image.jpg", use_det=True, use_rec=False)

# Custom parameters
result, elapse = ocr_engine(
    "image.jpg", 
    box_thresh=0.6,
    unclip_ratio=1.8,
    text_score=0.6
)
```

## Model Requirements

You need to prepare the following .om models:

1. **Detection Model**: `ch_PP-OCRv4_det_infer.om`
2. **Recognition Model**: `ch_PP-OCRv4_rec_infer.om`
3. **Character Dictionary**: `ppocr_keys_v1.txt`

Place these files in the `models/` directory as specified in the config.

## Dependencies

- `acl_net.py`: ACL_Net class for Ascend ACL inference
- Standard dependencies: `numpy`, `opencv-python`, `pillow`, `pyclipper`, `shapely`

## Notes

1. **ACL Device**: Make sure to set the correct `device_id` in the config
2. **Model Compatibility**: Ensure your .om models are compatible with your Ascend hardware
3. **Memory Management**: The ACL inference session properly handles resource cleanup
4. **Error Handling**: Comprehensive error handling for ACL-specific issues

## Troubleshooting

1. **Model Loading Errors**: Check if .om model files exist and are valid
2. **Device Errors**: Verify ACL device_id is available and accessible
3. **Memory Errors**: Ensure sufficient device memory for model inference
4. **Import Errors**: Make sure `acl_net.py` is in the Python path
