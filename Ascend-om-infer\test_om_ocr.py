#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
Test script for the ACL-based OCR pipeline
This script can be run directly from the root directory
"""
import argparse
import sys
from pathlib import Path

# Add the ocr_pipeline to Python path
sys.path.insert(0, str(Path(__file__).parent / "ocr_pipeline"))

from ocr_pipeline import OMOCR
from ocr_pipeline.utils import get_logger

logger = get_logger("TestOMOCR")


def main():
    parser = argparse.ArgumentParser(description="Test ACL-based OCR pipeline")
    parser.add_argument("--img_path", type=str, required=True, help="Path to input image")
    parser.add_argument("--config_path", type=str, default="ocr_pipeline/config.yaml", help="Path to config file")
    parser.add_argument("--print_cost", action="store_true", help="Print time cost")
    parser.add_argument("--no_det", action="store_true", help="Disable detection")
    parser.add_argument("--no_rec", action="store_true", help="Disable recognition")
    
    args = parser.parse_args()
    
    # Check if image exists
    img_path = Path(args.img_path)
    if not img_path.exists():
        logger.error(f"Image file does not exist: {img_path}")
        return
    
    # Check if config exists
    config_path = Path(args.config_path)
    if not config_path.exists():
        logger.error(f"Config file does not exist: {config_path}")
        return
    
    try:
        # Initialize OCR engine
        logger.info("Initializing ACL-based OCR engine...")
        ocr_engine = OMOCR(config_path=config_path)
        
        # Run OCR
        use_det = not args.no_det
        use_rec = not args.no_rec
        
        logger.info(f"Running OCR on {img_path}")
        logger.info(f"Detection: {use_det}, Recognition: {use_rec}")
        
        result, elapse_list = ocr_engine(
            str(args.img_path), 
            use_det=use_det, 
            use_rec=use_rec
        )
        
        # Print results
        if result:
            logger.info("OCR Results:")
            for i, res in enumerate(result):
                if use_det and use_rec:
                    box, text, score = res[0], res[1], res[2]
                    logger.info(f"  [{i}] Text: '{text}' (Score: {score:.3f})")
                    logger.info(f"       Box: {box}")
                elif use_det and not use_rec:
                    box = res
                    logger.info(f"  [{i}] Box: {box}")
                elif not use_det and use_rec:
                    text, score = res[0], res[1]
                    logger.info(f"  [{i}] Text: '{text}' (Score: {score:.3f})")
        else:
            logger.info("No text detected/recognized")
        
        if args.print_cost and elapse_list:
            logger.info(f"Time cost: {elapse_list}")
            
    except Exception as e:
        logger.error(f"Error during OCR processing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
