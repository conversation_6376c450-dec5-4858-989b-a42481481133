Global:
    text_score: 0.5
    use_det: true
    use_cls: false
    use_rec: true
    print_verbose: false
    min_height: 2000
    width_height_ratio: 0.8
    max_side_len: 2000
    min_side_len: 2000
    return_word_box: false

Det:
    device_id: 3  # ACL device ID, default 0
    model_path: /ai_home/AIDD/robot/weights/PP-OCRv4_server_det_infer_om_910B/inference.om  # Changed from .onnx to .om

    limit_side_len: 2000
    limit_type: max
    std: [ 0.5, 0.5, 0.5 ]
    mean: [ 0.5, 0.5, 0.5 ]

    thresh: 0.3
    box_thresh: 0.5
    max_candidates: 1000
    unclip_ratio: 1.8
    use_dilation: true
    score_mode: fast

Rec:
    device_id: 4  # ACL device ID, default 0
    model_path: /ai_home/AIDD/robot/weights/PP-OCRv4_server_rec_infer_om_910B/inference.om  # Changed from .onnx to .om
    rec_keys_path: /ai_home/AIDD/robot/repos/Ascend-om-infer/ocr_pipeline/ch_ppocr_rec/ppocr_keys_v1.txt  # Character dictionary file

    rec_img_shape: [3, 48, 320]
    rec_batch_num: 6
