# -*- encoding: utf-8 -*-
# @Author: SWHL
# @Contact: <EMAIL>
from pathlib import Path
from typing import Dict, Union

import yaml

from .acl_infer_session import AclInferSession
from .load_image import LoadImage, LoadImageError
from .logger import get_logger
from .process_img import add_round_letterbox, increase_min_side, reduce_max_side


def read_yaml(yaml_path: Union[str, Path]) -> Dict[str, Dict]:
    with open(yaml_path, "rb") as f:
        data = yaml.load(f, Loader=yaml.Loader)
    return data
