#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
Example usage of the ACL-based OCR pipeline
This script can be run directly from the root directory
"""
import sys
from pathlib import Path

# Add the ocr_pipeline to Python path
sys.path.insert(0, str(Path(__file__).parent / "ocr_pipeline"))

from ocr_pipeline import OMOCR
from ocr_pipeline.utils import get_logger

logger = get_logger("OMOCRExample")


def main():
    """Example of how to use the ACL-based OCR pipeline"""
    
    # Initialize OCR engine with config
    config_path = "ocr_pipeline/config.yaml"
    logger.info("Initializing ACL-based OCR engine...")
    
    try:
        ocr_engine = OMOCR(config_path=config_path)
        logger.info("OCR engine initialized successfully!")
        
        # Example image path (you need to provide a real image)
        img_path = "/ai_home/AIDD/robot/repos/Ascend-om-infer/imgs/slg.png"
        
        if not Path(img_path).exists():
            logger.warning(f"Test image {img_path} not found. Please provide a valid image path.")
            logger.info("Usage example:")
            logger.info("  result, elapse = ocr_engine('your_image.jpg')")
            return
        
        # Run OCR with both detection and recognition
        logger.info(f"Running OCR on {img_path}...")
        result, elapse_list = ocr_engine(img_path, use_det=True, use_rec=True)
        
        # Display results
        if result:
            logger.info(f"OCR completed! Found {len(result)} text regions.")
            for i, (box, text, score) in enumerate(result):
                logger.info(f"  [{i+1}] Text: '{text}' (Confidence: {score:.3f})")
                logger.info(f"       Box coordinates: {box}")
        else:
            logger.info("No text detected in the image.")
        
        # Display timing information
        if elapse_list:
            det_time, cls_time, rec_time = elapse_list
            logger.info(f"Timing: Detection={det_time:.3f}s, Classification={cls_time:.3f}s, Recognition={rec_time:.3f}s")
            logger.info(f"Total time: {sum(elapse_list):.3f}s")
            
    except Exception as e:
        logger.error(f"Error during OCR processing: {e}")
        import traceback
        traceback.print_exc()


def example_detection_only():
    """Example of detection-only mode"""
    logger.info("\n=== Detection Only Example ===")
    
    ocr_engine = OMOCR()
    img_path = "test_image.jpg"
    
    if Path(img_path).exists():
        result, elapse_list = ocr_engine(img_path, use_det=True, use_rec=False)
        
        if result:
            logger.info(f"Detected {len(result)} text regions:")
            for i, box in enumerate(result):
                logger.info(f"  [{i+1}] Box: {box}")
        else:
            logger.info("No text regions detected.")


def example_recognition_only():
    """Example of recognition-only mode (requires pre-cropped text images)"""
    logger.info("\n=== Recognition Only Example ===")
    
    ocr_engine = OMOCR()
    img_path = "cropped_text.jpg"  # Should be a cropped text image
    
    if Path(img_path).exists():
        result, elapse_list = ocr_engine(img_path, use_det=False, use_rec=True)
        
        if result:
            for text, score in result:
                logger.info(f"Recognized text: '{text}' (Confidence: {score:.3f})")
        else:
            logger.info("No text recognized.")


if __name__ == "__main__":
    main()
    
    # Uncomment to run additional examples
    # example_detection_only()
    # example_recognition_only()
