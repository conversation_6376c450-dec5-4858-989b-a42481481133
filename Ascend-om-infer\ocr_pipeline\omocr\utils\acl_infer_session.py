# -*- encoding: utf-8 -*-
# @Author: SWHL
# @Contact: <EMAIL>
import traceback
from pathlib import Path
from typing import Any, Dict, List, Union

import numpy as np

from .logger import get_logger


class AclInferSession:
    """ACL inference session wrapper that provides the same interface as OrtInferSession"""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = get_logger("AclInferSession")

        model_path = config.get("model_path", None)
        self._verify_model(model_path)

        # Use device_id from config, default to 0 (following inference.py pattern)
        device_id = config.get("device_id", 0)

        # Import ACL_Net here to avoid circular imports
        import sys
        import os
        # Add the parent directory to sys.path to find acl_net
        parent_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)
        from acl_net import ACL_Net

        try:
            # Initialize ACL_Net with model path and device_id
            self.session = ACL_Net(model_path, device_id)
            self.logger.info(f"Successfully loaded ACL model: {model_path} on device {device_id}")
        except Exception as e:
            self.logger.error(f"Failed to load ACL model: {model_path}")
            raise AclRuntimeError(f"Failed to initialize ACL model: {str(e)}") from e
    
    def __call__(self, input_content: np.ndarray) -> List[np.ndarray]:
        """
        Run inference on input data

        Args:
            input_content: Input numpy array

        Returns:
            List of output numpy arrays
        """
        try:
            # ACL_Net.forward expects a list of inputs (following inference.py pattern)
            if isinstance(input_content, np.ndarray):
                inputs = [input_content]
            else:
                inputs = input_content

            # Run inference using ACL_Net.forward([input])
            results = self.session.forward(inputs)

            # ACL_Net.forward returns a list, ensure we return the same format
            if not isinstance(results, list):
                results = [results]

            return results

        except Exception as e:
            error_info = traceback.format_exc()
            self.logger.error(f"ACL inference failed: {error_info}")
            raise AclRuntimeError(error_info) from e
    
    def get_input_names(self) -> List[str]:
        """Get input names (placeholder for compatibility)"""
        # ACL_Net doesn't provide input names, so we return generic names
        return [f"input_{i}" for i in range(1)]  # Assume single input for most models
    
    def get_output_names(self) -> List[str]:
        """Get output names (placeholder for compatibility)"""
        # ACL_Net doesn't provide output names, so we return generic names
        return [f"output_{i}" for i in range(1)]  # Assume single output for most models
    
    def get_character_list(self, key: str = "character") -> List[str]:
        """
        Get character list from model metadata
        Note: ACL models don't have metadata like ONNX, so this returns None
        """
        self.logger.warning("ACL models don't support metadata. Character list should be provided via config.")
        return None
    
    def have_key(self, key: str = "character") -> bool:
        """
        Check if model has metadata key
        Note: ACL models don't have metadata like ONNX
        """
        return False
    
    @staticmethod
    def _verify_model(model_path: Union[str, Path, None]):
        """Verify model file exists and is valid"""
        if model_path is None:
            raise ValueError("model_path is None!")
        
        model_path = Path(model_path)
        if not model_path.exists():
            raise FileNotFoundError(f"{model_path} does not exist.")
        
        if not model_path.is_file():
            raise FileExistsError(f"{model_path} is not a file.")
        
        # Check if it's an .om file
        if model_path.suffix.lower() != '.om':
            raise ValueError(f"Expected .om file, got {model_path.suffix}")

    def __del__(self):
        """Destructor to properly release ACL resources"""
        try:
            if hasattr(self, 'session') and self.session is not None:
                # ACL_Net has its own __del__ method that handles resource cleanup
                del self.session
                self.session = None
        except Exception as e:
            # Log error but don't raise exception in destructor
            if hasattr(self, 'logger'):
                self.logger.warning(f"Error during ACL resource cleanup: {e}")


class AclRuntimeError(Exception):
    """Exception raised when ACL runtime encounters an error"""
    pass
