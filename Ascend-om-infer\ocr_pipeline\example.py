#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
Example usage of the ACL-based OCR pipeline
"""
from pathlib import Path

from main import OMOCR
from utils import get_logger

logger = get_logger("OCRExample")


def main():
    """Example of how to use the ACL-based OCR pipeline"""
    
    # Initialize OCR engine with config
    config_path = "config.yaml"
    logger.info("Initializing ACL-based OCR engine...")
    
    try:
        ocr_engine = OMOCR(config_path=config_path)
        logger.info("OCR engine initialized successfully!")
        
        # Example image path (you need to provide a real image)
        img_path = "test_image.jpg"
        
        if not Path(img_path).exists():
            logger.warning(f"Test image {img_path} not found. Please provide a valid image path.")
            logger.info("Usage example:")
            logger.info("  result, elapse = ocr_engine('your_image.jpg')")
            return
        
        # Run OCR with both detection and recognition
        logger.info(f"Running OCR on {img_path}...")
        result, elapse_list = ocr_engine(img_path, use_det=True, use_rec=True)
        
        # Display results
        if result:
            logger.info(f"OCR completed! Found {len(result)} text regions.")
            for i, (box, text, score) in enumerate(result):
                logger.info(f"  [{i+1}] Text: '{text}' (Confidence: {score:.3f})")
                logger.info(f"       Box coordinates: {box}")
        else:
            logger.info("No text detected in the image.")
        
        # Display timing information
        if elapse_list:
            det_time, cls_time, rec_time = elapse_list
            logger.info(f"Timing: Detection={det_time:.3f}s, Classification={cls_time:.3f}s, Recognition={rec_time:.3f}s")
            logger.info(f"Total time: {sum(elapse_list):.3f}s")
            
    except Exception as e:
        logger.error(f"Error during OCR processing: {e}")
        import traceback
        traceback.print_exc()


def example_detection_only():
    """Example of detection-only mode"""
    logger.info("\n=== Detection Only Example ===")
    
    ocr_engine = OMOCR()
    img_path = "test_image.jpg"
    
    if Path(img_path).exists():
        result, elapse_list = ocr_engine(img_path, use_det=True, use_rec=False)
        
        if result:
            logger.info(f"Detected {len(result)} text regions:")
            for i, box in enumerate(result):
                logger.info(f"  [{i+1}] Box: {box}")
        else:
            logger.info("No text regions detected.")


def example_recognition_only():
    """Example of recognition-only mode (requires pre-cropped text images)"""
    logger.info("\n=== Recognition Only Example ===")
    
    ocr_engine = OMOCR()
    img_path = "cropped_text.jpg"  # Should be a cropped text image
    
    if Path(img_path).exists():
        result, elapse_list = ocr_engine(img_path, use_det=False, use_rec=True)
        
        if result:
            for text, score in result:
                logger.info(f"Recognized text: '{text}' (Confidence: {score:.3f})")
        else:
            logger.info("No text recognized.")


def example_custom_parameters():
    """Example with custom parameters"""
    logger.info("\n=== Custom Parameters Example ===")
    
    ocr_engine = OMOCR()
    img_path = "test_image.jpg"
    
    if Path(img_path).exists():
        # Custom parameters for better detection/recognition
        result, elapse_list = ocr_engine(
            img_path,
            box_thresh=0.6,      # Higher threshold for detection boxes
            unclip_ratio=1.8,    # Larger unclip ratio for detection
            text_score=0.6,      # Higher confidence threshold for final results
            return_word_box=True # Return character-level boxes
        )
        
        if result:
            logger.info("OCR with custom parameters:")
            for i, res in enumerate(result):
                if len(res) >= 3:
                    box, text, score = res[0], res[1], res[2]
                    logger.info(f"  [{i+1}] Text: '{text}' (Score: {score:.3f})")
                    if len(res) > 3:  # Has word boxes
                        logger.info(f"       Character boxes available")


if __name__ == "__main__":
    main()
    
    # Uncomment to run additional examples
    # example_detection_only()
    # example_recognition_only() 
    # example_custom_parameters()
