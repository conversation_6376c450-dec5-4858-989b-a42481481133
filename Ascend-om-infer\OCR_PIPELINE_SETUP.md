# ACL-based OCR Pipeline Setup Guide

## 📁 项目结构

```
Ascend-om-infer/
├── acl_net.py                      # ACL推理引擎
├── test_om_ocr.py                  # 主测试脚本
├── example_om_ocr.py               # 使用示例脚本
└── ocr_pipeline/                   # OCR管道包
    ├── __init__.py                 # 包初始化
    ├── main.py                     # 主OCR类 (OMOCR)
    ├── config.yaml                 # 配置文件
    ├── README.md                   # 详细文档
    ├── utils/                      # 工具模块
    │   ├── __init__.py
    │   ├── acl_infer_session.py    # ACL推理会话封装
    │   ├── load_image.py           # 图像加载
    │   ├── logger.py               # 日志工具
    │   ├── process_img.py          # 图像处理
    │   └── vis_res.py              # 可视化工具
    ├── ch_ppocr_det/               # 检测模块
    │   ├── __init__.py
    │   ├── text_detect.py          # 文本检测器
    │   └── utils.py                # 检测预处理/后处理
    ├── ch_ppocr_rec/               # 识别模块
    │   ├── __init__.py
    │   ├── text_recognize.py       # 文本识别器
    │   └── utils.py                # 识别预处理/后处理
    └── cal_rec_boxes/              # 字符框计算
        ├── __init__.py
        └── main.py                 # 字符级别框计算
```

## 🔧 已修复的引用问题

### 1. ACL_Net 导入问题
- **问题**: `from acl_net import ACL_Net` 找不到模块
- **解决**: 在 `acl_infer_session.py` 中动态添加路径到 `sys.path`

### 2. 包内相对导入问题
- **问题**: 模块间相对导入路径错误
- **解决**: 统一使用 `from ..utils import` 格式的相对导入

### 3. 测试脚本导入问题
- **问题**: 测试脚本无法直接运行
- **解决**: 创建独立的测试脚本 `test_om_ocr.py` 和 `example_om_ocr.py`

## 🚀 使用方法

### 方法1: 直接运行测试脚本

```bash
# 从根目录运行
cd Ascend-om-infer

# 测试OCR管道
python test_om_ocr.py --img_path /path/to/image.jpg --config_path ocr_pipeline/config.yaml --print_cost

# 运行示例
python example_om_ocr.py
```

### 方法2: 在Python代码中使用

```python
import sys
from pathlib import Path

# 添加ocr_pipeline到Python路径
sys.path.insert(0, str(Path(__file__).parent / "ocr_pipeline"))

from ocr_pipeline import OMOCR

# 初始化OCR引擎
ocr_engine = OMOCR(config_path="ocr_pipeline/config.yaml")

# 运行OCR
result, elapse_list = ocr_engine("image.jpg")

# 处理结果
if result:
    for box, text, score in result:
        print(f"Text: {text}, Score: {score}")
```

## ⚙️ 配置文件

`ocr_pipeline/config.yaml` 已配置为使用 `.om` 模型：

```yaml
Global:
    use_det: true
    use_cls: false    # 已禁用分类模块
    use_rec: true

Det:
    device_id: 0      # ACL设备ID
    model_path: models/ch_PP-OCRv4_det_infer.om

Rec:
    device_id: 0      # ACL设备ID  
    model_path: models/ch_PP-OCRv4_rec_infer.om
    rec_keys_path: models/ppocr_keys_v1.txt
```

## 📋 所需模型文件

请准备以下文件并放在 `models/` 目录下：

1. **检测模型**: `ch_PP-OCRv4_det_infer.om`
2. **识别模型**: `ch_PP-OCRv4_rec_infer.om`  
3. **字符字典**: `ppocr_keys_v1.txt`

## 🔍 关键修复点

### 1. AclInferSession 类
- 正确封装了 ACL_Net 的调用方式
- 遵循 `inference.py` 中的使用模式：`model.forward([input])`
- 实现了与 OrtInferSession 相同的接口

### 2. 包结构优化
- 所有模块使用正确的相对导入
- 创建了可直接运行的测试脚本
- 保持了与 rapidocr_onnxruntime 相同的API

### 3. 错误处理
- 完善的ACL相关错误处理
- 资源自动清理机制
- 详细的日志输出

## ✅ 验证步骤

1. **检查文件结构**: 确保所有文件都在正确位置
2. **准备模型文件**: 将 `.om` 模型文件放在 `models/` 目录
3. **运行测试**: 使用 `test_om_ocr.py` 测试基本功能
4. **检查日志**: 观察ACL初始化和推理过程的日志输出

## 🐛 常见问题

1. **ACL_Net 导入失败**: 确保 `acl_net.py` 在根目录
2. **模型加载失败**: 检查 `.om` 文件路径和权限
3. **设备错误**: 验证 ACL 设备ID 是否可用
4. **内存不足**: 确保设备有足够内存加载模型

现在所有的引用问题都已修复，OCR管道可以正常使用了！
